ActiveAdmin.register Company, as: "Companies AI Usage Cost" do
  menu parent: 'Companies', priority: 8, label: "AI Usage Cost"
  breadcrumb do
    ['admin', 'companies']
  end
  actions :index

  filter :name_or_subdomain_contains, :as => :string

  index do
    id_column
    column :name
    column :subdomain
    column("Total Cost", sortable: :ai_usage_cost) do |company|
      "$#{company.ai_usage_cost}"
    end
  end
end
