<template>
  <div>
    <slot name="label" />
    <div>
      <multi-select
        ref="input"
        placeholder="Select Category"
        :value="valueStr"
        :disabled="!canEdit"
        :multiple="false"
        :options="options"
        :taggable="true"
        :allow-empty="!field.required"
        @remove="removeValue"
        @select="changeValue"
      />
    </div>
    <div 
      v-if="isWrite"
      class="text-muted smallest pt-1"
    >
      Want to add a new category?
      <a
        href="#"
        @click.prevent.stop="openCategoryModal"
      >
        Add new
      </a>
    </div>
    <create-category-modal
      v-if="showCategoryModal"
      ref="createCategoryModal"
      @fetch-categories="fetchCategoryOptions"
      @set-category-name="changeValue"
      @close="closeCategoryModal"
    />
  </div>
</template>

<script>
  import customForms from 'mixins/custom_forms';
  import { mapGetters, mapActions } from 'vuex';
  import _get from 'lodash/get';
  import fieldPermissions from 'mixins/custom_forms/field_permissions';
  import MultiSelect from 'vue-multiselect';
  import _map from 'lodash/map';
  import CreateCategoryModal from "../../create_category_modal.vue";

  export default {
    components: {
      MultiSelect,
      CreateCategoryModal,
    },
    mixins: [customForms, fieldPermissions],
    props: ['field', 'value', 'object'],
    data() {
      return {
        showCategoryModal: false,
      };
    },
    computed: {
      ...mapGetters({
        categories: 'customForms/categoryOptions',
      }),
      options() {
        return  _map(this.categories, 'name');
      },
      valueStr() {
        return _get(this, "value[0].valueStr", "");
      },
    },
    methods: {
      ...mapActions({
        fetchCategoryOptions: 'customForms/fetchCategoryOptions',
      }),

      onWorkspaceChange() {
        if (!this.categories || !this.categories.length) {
          this.fetchCategoryOptions();
        }
      },
      changeValue(value) {
        this.addFormValue(this.object.id, {
          custom_form_field_id: this.field.id,
          value,
          name: this.field.name,
        });
      },
      removeValue(value) {
        this.removeFormValue(this.object.id, {
          custom_form_field_id: this.field.id,
          value,
          name: this.field.name,
        });
      },
      openCategoryModal() {
        this.showCategoryModal = true;
        this.$nextTick(() => {
          if (this.$refs.createCategoryModal) {
            this.$refs.createCategoryModal.open();
          }
        });
      },
      closeCategoryModal() {
        this.showCategoryModal = false;
      },
    },
  };
</script>
