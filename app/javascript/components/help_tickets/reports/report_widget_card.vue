<template>
  <div 
    v-if="metricData && metricData.length > 0"
    class="widget-container bg-very-light"
  >
    <div
      v-if="isEditMode"
      v-tooltip="{
        content: 'Drag card to change the order',
        placement: 'top-start',
        delay: { show: 300 }
      }"
      class="handle drag-overlay"
    >
      <span class="handle-dots genuicon-draggable"/>
    </div>
    <div
      v-if="isEditMode && viewMode !== 'printable'"
      v-tooltip="'Remove this card'"
      class="remove-card"
      @click.stop.prevent="removeCard"
    >
      &times;
    </div>
    <button
      v-if="isEditMode && viewMode !== 'printable'"
      class="expand-icon-button shadow-none ml-n1"
      @click="openEditModal"
    >
      <i class="nulodgicon-edit mt-1" />
    </button>
    <button
      v-else-if="viewMode !== 'printable'"
      class="expand-icon-button shadow-none ml-n1"
      @click="openCardOptionsDropdown"
    >
      <i class="genuicon-expand mt-1" />
    </button>
    <div class="data-container">
      <span class="title pl-2">
        {{ name }}
        <span
          v-if="widget.comparePreviousTimeperiod && !isCustomTimeframe"
          class="category-pill py-2"
        >
          <span
            class="not-as-small mb-0"
            :style="numericData.color ? { color: numericData.color } : {}"
          >
            {{ getPreviousTimeperiodDate }}
          </span>
        </span>
      </span>
      <pulse-loader
        v-if="isLoading"
        class="ml-2"
        color="#0d6efd"
        size="0.5rem"
        :loading="true"
      />
      <span v-else-if="!metricData[0].previewData">
        No data available
      </span>
      <!-- <span
        v-else-if="widgetChartType === 'numeric'"
        class="data"
      >
        {{ numericData }}
      </span> -->

      <div v-else-if="widgetChartType() === 'numeric'">
        <div 
          class="w-100 pl-3 d-flex scroll-wrapper" 
        >
          <div class="numeric-scroll-content mt-3">
            <div 
              v-for="(numericData, index) in filteredNumericPreviewData"
              :key="index"
              class="d-flex flex-column mr-4"
            >
              <span 
                class="category-pill"
                :style="numericData.color ? { background: hexToRgba(numericData.color, 0.15), color: numericData.color } : {}"
              >
                <span class="mr-1">•</span>
                <span 
                  v-tooltip="{
                    content: numericData.category,
                    show: (numericData.category.length > 19 && hoveredIndex == numericData.category),
                    trigger: 'manual'
                  }"
                  class="not-as-small mb-0"
                  :style="numericData.color ? { color: numericData.color } : {}"
                  @mouseover="hoveredIndex = numericData.category"
                  @mouseleave="hoveredIndex = null"
                >
                  {{ truncate(numericData.category, 20) }}
                </span>

              </span>
              <div>
                <span class="numeric-data">{{ numericData.value }}</span>
                <span v-if="widget.comparePreviousTimeperiod">
                  <i
                    v-if="numericData.higher"
                    class="nulodgicon-arrow-up-b higher-data-label"
                  />
                  <i
                    v-else
                    class="nulodgicon-arrow-down-b lower-data-label"
                  />
                  <span
                    :class="{ 'higher-data-label': numericData.higher, 'lower-data-label': !numericData.higher }" 
                    class="previous-timeperiod-data"
                  >
                    {{ numericData.timeperiodValue }}
                  </span>
                </span>
              </div>
            </div>
          </div>

        </div>
      </div>

      <div
        v-else
        class="chart-container"
      >
        <apex-chart
          v-if="metricData"
          :key="`${metricData[0].id}_${isDarkMode}`"
          :width="chartWidth"
          :height="chartHeight"
          :type="widgetChartType()"
          :options="options"
          :series="series"
        />
      </div>
    </div>

    <Teleport to="body">
      <sweet-modal
        ref="editWidgetModal"
        v-sweet-esc
        :title="'Customze Your Chart'"
        modal-theme="right theme-dark-header theme-sticky-footer"
        :width="'75%'"
      >
        <div>
          <customize-widget-card
            ref="customizeWidget"
            :widget="widget"
            :modal-opened="customizeModalOpened"
            @finish-editing="handleFinishEditing"
            @update-unselected-fields="handleUnselectedFields"
          />
        </div>
      </sweet-modal>
    </Teleport>

    <Teleport to="body">
      <sweet-modal
        ref="viewDetail"
        v-sweet-esc
        modal-theme="right theme-wide"
        :width="'70%'"
      >
        <template slot="title">
          <div class="d-flex align-items-center justify-content-between h-100">
            <div class="d-flex align-items-center">
              <span
                v-if="data.icon"
                class="h5 mr-1 ml-n3 mb-0 mt-1"
                :class="data.icon"
              />
              <h4 class="mb-0">{{ name }}</h4>
              <h5 class="mb-0 ml-2">{{ timeperiodDate }}</h5>
            </div>
            <button
              type="button"
              class="btn btn-primary ml-3 export-btn"
              @click.prevent="downloadXLSX"
            >
              <i class="nulodgicon-cloud-download mr-1" />
              Export
            </button>
          </div>
        </template>
        <cards-tickets-details
          ref="insightDetail"
          :widget="widget"
        />
      </sweet-modal>
    </Teleport>

  </div>
</template>

<script>
  import http from 'common/http';
  import strings from 'mixins/string';
  import { SweetModal } from 'sweet-modal-vue';
  import ApexChart from 'vue-apexcharts';
  import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
  import { mapGetters } from 'vuex';
  import dates from 'mixins/dates';
  import CustomizeWidgetCard from './customize_widget_card.vue';
  import CardsTicketsDetails from "./cards_tickets_details.vue";

  export default {
    components: {
      SweetModal,
      PulseLoader,
      CustomizeWidgetCard,
      ApexChart,
      CardsTicketsDetails,
    },
    mixins: [strings, dates],
    props: {
      widgetId: {
        type: Number,
        default: null,
      },
      name: {
        type: String,
        default: 'Default title',
      },
      data: {
        type: Number,
        default: 100,
      },
      widget: {
        type: Object,
        default: null,
      },
      isEditMode: {
        type: Boolean,
        default: false,
      },
      isLoading: {
        type: Boolean,
        default: false,
      },
      viewMode: {
        type: String,
        default: 'dashboard',
      },
    },
    data() {
      return {
        customizeModalOpened: false,
        hoveredIndex: null,
        priorityOptions: [],
        statusOptions: [],
        metricData: this.widget?.metrics ? this.widget.metrics : null,
        cardWidth: this.widget.cardAttributes.w,
        previewCartWidth: this.widget.previewAttributes?.w,
        displayLabels: this.widget.displayLabels || false,
        isHorizontalBar: false,
        zoomFactor: 1,
        defaultOptions: {
          chart: {
            id: 'preview-chart',
            animations: {
              enabled: false,
              speed: 500,
            },
            animateGradually: {
                enabled: false,
            },
          },
          colors: this.insightCardColors,
          xaxis: {
            categories: ["Low", "Medium", "High"],
          },
          dataLabels: {
            enabled: this.displayLabels,
          },
        },
        defaultSeries: [
          {
            name: 'series-1',
            data: [30, 40, 45],
          }, 
        ],
        insightCardColors: [
          "#8871c3", "#5097be", "#70aea3", "#dbbf5c",
          "#b74458", "#614b9b", "#878ecf", "#6477ba",
          "#51709f", "#467d9b", "#70b4ca", "#8abdc0",
          "#6ebd8e", "#a2ca7f", "#d5d770", "#dc9f45",
          "#c97324", "#bf4c27", "#b56098", "#8B569A",
        ],
        selectedCategories: [],
        tempSelectedCategories: [],
        isDarkMode: false,
      };
    },
    
    computed: {
      ...mapGetters('inDepthTicketReport', [
        'timeframe',
      ]),
      getPreviousTimeperiodDate() {
        if (this.timeframename === "custom_date_range") {
          return "";
        } else if (this.timeframe !== null) {
          const timePeriod = this.previousTimeperiodDates(this.timeframe);
          const currentYear = moment().year();

          const startYear = moment(timePeriod.startDate).year();
          const endYear = moment(timePeriod.endDate).year();

          const startDate = moment(timePeriod.startDate).format(
            startYear === currentYear ? "MMM DD" : "MMM DD, 'YY"
          );
          const endDate = moment(timePeriod.endDate).format(
            endYear === currentYear ? "MMM DD" : "MMM DD, 'YY"
          );

          if (startDate === endDate) {
            return `Compares to: ${startDate}`;
          }

          return `Compares to: ${startDate} - ${endDate}`;
        }
        return "";
      },
      isCustomTimeframe() {
        if (this.timeframe.name === "custom_date_range") {
          return true;
        }
        return false;
      },
      timeperiodDate() {
        const timePeriod = this.timeframeDates(this.timeframe);
        if (this.timeframe) {
        const startDate = moment(timePeriod.startDate).format('MMM DD, YYYY');
        const endDate = moment(timePeriod.endDate).format('MMM DD, YYYY');
        return `(${startDate} - ${endDate})`;
        }
        return this.timeframe;
      },
      options() {
        if (this.widgetChartType() === "donut" || this.widgetChartType() === "pie") {
          if (this.metricData[0]?.previewCategories) {
            return {
              labels: this.metricData[0]?.previewCategories,
              colors: this.insightCardColors,
              dataLabels: {
                enabled: this.displayLabels,
              },
              plotOptions: {
                pie: {
                  expandOnClick: false,
                },
              },
              legend: {
                position: 'bottom',
                horizontalAlign: 'center',
                labels: {
                    colors: this.isDarkMode ? '#ebeef1' : '#212529',
                },
              },
            };
          };
          return {
            labels: ['Low', 'Medium', 'High'],
          };
        }
        
        if (this.widgetChartType() === "bar" && this.metricData[0]?.previewCategories) {
          return {
            chart: {
              toolbar: {
                show: false,
              },
              type: 'bar',
              id: 'preview-chart',
              animations: {
                enabled: false,
                speed: 500,
              },
              animateGradually: { enabled: false },
            },
            plotOptions: {
              bar: {
                horizontal: this.isHorizontalBar,
                barHeight: this.isHorizontalBar ? "30%" : undefined,
              },
            },
            colors: this.insightCardColors,
            xaxis: {
              categories: this.metricData[0]?.previewCategories,
              labels: {
                formatter: (val) => val.length > 15 ? `${val.slice(0, 13)  }...` : val,
                style: {
                  colors: this.isDarkMode ? '#ebeef1' : '#212529',
                  fontSize: '12px',
                },
              },
            },
            yaxis: {
              labels: {
                formatter: (val) => val.length > 20 ? `${val.slice(0, 17)  }...` : val,
                style: {
                  colors: this.isDarkMode ? '#ebeef1' : '#212529',
                  fontSize: '12px',
                },
              },
            },
            tooltip: {
              shared: true,
              intersect: false,
              x: {
                formatter: (val, opts) => 
                   this.metricData[0]?.previewCategories?.[opts.dataPointIndex] || val
                ,
              },
              theme: this.isDarkMode ? "dark" : "light",
              fixed: {
                enabled: this.widget.cardAttributes.x === 0 && this.widget.cardAttributes.w < 6,
                position: 'topLeft',
                offsetX: 100,
                offsetY: 0,
              },
            },
            legend: {
              labels: {
                colors: this.isDarkMode ? '#ebeef1' : '#212529',
              },
            },
            dataLabels: {
              enabled: this.displayLabels,
            },
          };
        }
        if (this.metricData[0]?.previewCategories) {
          return {
          chart: {
            id: 'preview-chart',
            toolbar: {
              show: false,
            },
            zoom: {
              enabled: false,
            },
            selection: {
              enabled: false,
            },
            animations: {
              enabled: false,
              speed: 500,
            },
            animateGradually: {
                enabled: false,
            },
          },
          // colors: this.insightCardColors,
          colors: this.widgetChartType() === "heatmap" ? ["#8871c3"] : this.insightCardColors,
          xaxis: {
            categories: this.metricData[0]?.previewCategories,
            labels: {
              formatter: (val) => typeof val === 'string' && val.length > 20 ? `${val.slice(0, 17)}...` : (val ?? ''),
              style: {
                colors: this.isDarkMode ? '#ebeef1' : '#212529',
                fontSize: '12px',
              },
            },
          },
          yaxis: {
            labels: {
              style: {
                colors: this.isDarkMode ? '#ebeef1' : '#212529',
                fontSize: '12px',
              },
            },
          },
          dataLabels: {
            enabled: this.displayLabels,
            dropShadow: {
              enabled: true,
              top: 0.5,
              left: 0.5,
              color: '#000',
            },
          },
          legend: {
            labels: {
                colors: this.isDarkMode ? '#ebeef1' : '#212529',
            },
          },
          tooltip: {
            x: {
              formatter: (val, opts) => 
                  this.metricData[0]?.previewCategories?.[opts.dataPointIndex] || val,
            },
            theme: this.isDarkMode ? "dark" : "light",
          },
          };
        };
        return this.defaultOptions;
      },
      series() {
        if (this.widgetChartType() === "donut" || this.widgetChartType() === "pie") {
          const donutseries = this.metricData[0]?.previewData;
          if (donutseries) {
            return donutseries[0].data;
          }
          return [44, 55, 41];
        }
        if (this.metricData?.length) {
          const combinedSeries = this.metricData.flatMap(metric => metric.previewData || []);
          return combinedSeries.length ? combinedSeries : this.defaultSeries;
        }
        return this.defaultSeries;
      },
      chartWidth() {
        if (this.viewMode === "printable") {
          const temp1 = 30;
          const temp2 = 35;
          if (this.widgetChartType() === "donut" || this.widgetChartType() === "pie") {
            return this.widget.previewAttributes.w * temp1;
          }
          return this.widget.previewAttributes.w * temp2;
        }
        const zoomLevel = this.zoomFactor * 100;
        let factor80 = 80;
        let factor90 = 90;

        if (zoomLevel >= 110 && zoomLevel < 125) {
          factor80 -= 10;
          factor90 -= 20;
        } else if (zoomLevel >= 125 && zoomLevel < 150) {
          factor80 -= 20;
          factor90 -= 23;
        } else if (zoomLevel >= 150) {
          factor80 -= 20;
          factor90 -= 40;
        }

        return this.widgetChartType() === "donut" || this.widgetChartType() === "pie"
          ? this.widget.cardAttributes.w * factor80
          : this.widget.cardAttributes.w * factor90;
      },
      chartHeight() {
        const isPrintable = this.viewMode === "printable";
        const isCircularChart = this.widgetChartType() === "donut" || this.widgetChartType() === "pie";
        const heightSource = isPrintable ? this.widget.previewAttributes.h : this.widget.cardAttributes.h;
        const multiplier = isCircularChart ? 30 : 35;

        return heightSource * multiplier;
      },
      numericData() {
        const previewData = this.metricData ? this.metricData[0]?.previewData : null;
        let dataSum;
        if (previewData?.length) {
          dataSum = previewData[0].data?.reduce((sum, num) => sum + num, 0);
        }
        return `${ dataSum || 0 }`;
      },
      previousTimeperiodData() {
        const previewData = this.metricData ? this.metricData[0]?.previewData : null;
        let dataSum;
        if (previewData) {
          dataSum = previewData[1].data?.reduce((sum, num) => sum + num, 0);
        }
        return `${ dataSum || 0 }`;
      },
      numericDataGreater() {
        if (this.numericData > this.previousTimeperiodData) {
          return true;
        }
          return false;
      },
      comparisonData() {
        if (this.numericData > this.previousTimeperiodData) {
          return this.numericData - this.previousTimeperiodData;
        }
        return this.previousTimeperiodData - this.numericData;
      },
      // numericPreviewData() {
      //   const priorityColors = {};
      //   const statusColors = {};
      //   if (this.metricData[0].type === "priority") {
      //     this.priorityOptions?.forEach(option => {
      //       priorityColors[option.name.toLowerCase()] = option.color;
      //     });
      //   }
      //   if (this.metricData[0].type === "status") {
      //     this.statusOptions?.forEach(option => {
      //       statusColors[option.name] = option.color;
      //     });
      //   }
      //   return this.metricData[0]?.previewCategories.map((category, index) => {
      //     const value = this.metricData[0]?.previewData[0]?.data[index] || 0;
      //     const previousValue = this.metricData[0]?.previewData[1]?.data[index] || 0;
      //     const timeperiodValue = Math.abs(value - previousValue);
      //     const higher = value > previousValue;

      //     const base = {
      //       category,
      //       value,
      //       timeperiodValue,
      //       higher,
      //     };

      //     if (this.metricData[0].type === "priority") {
      //       base.color = priorityColors[category] || "#343A40";
      //     }
      //     else if (this.metricData[0].type === "status") {
      //       base.color = statusColors[category] || "#343A40";
      //     }
      //     return base;
      //   });
      // },
      numericPreviewData() {
        const priorityColors = {};
        const statusColors = {};

        const metric = this.metricData[0];
        if (!metric) return [];

        if (metric.type === "priority") {
          this.priorityOptions?.forEach(option => {
            priorityColors[option.name.toLowerCase()] = option.color;
          });
        }

        if (metric.type === "status") {
          this.statusOptions?.forEach(option => {
            statusColors[option.name] = option.color;
          });
        }

        const previewData = metric?.previewData?.[0]?.data || [];
        const previousData = metric?.previewData?.[1]?.data || [];

        const combined = metric.previewCategories.map((category, index) => {
          const value = previewData[index] || 0;
          const previousValue = previousData[index] || 0;
          const timeperiodValue = Math.abs(value - previousValue);
          const higher = value > previousValue;

          const base = {
            category,
            value,
            timeperiodValue,
            higher,
          };

          if (metric.type === "priority") {
            base.color = priorityColors[category.toLowerCase()] || "#343A40";
          } else if (metric.type === "status") {
            base.color = statusColors[category] || "#343A40";
          }

          return base;
        });

        return combined.sort((a, b) => b.value - a.value);
      },
      filteredNumericPreviewData() {
        if (!this.selectedCategories.length) return this.numericPreviewData;
        return this.numericPreviewData.filter(item =>
          this.selectedCategories.includes(item.category)
        );
      },
      isTemplate() {
        const {path} = this.$route;
        return path.includes('/report_templates');
      },
    },
    watch: {
      'widget.metrics': {
        handler(newVal) {
          try {
            this.metricData = typeof newVal === 'string' ? JSON.parse(newVal) : newVal || null;
          } catch (error) {
            console.error('Failed to parse widget.metrics:', error);
            this.metricData = null;
          }
        },
        immediate: true,
        deep: true,
      },
      'widget.displayLabels': {
        handler(newVal) {
          this.displayLabels = newVal;
        },
        immediate: true,
        deep: true,
      },
      numericPreviewData: {
        handler(newVal) {
          const unselected = this.widget?.unselectedFields || [];
          this.selectedCategories = newVal
            .map(d => d.category)
            .filter(category => !unselected.includes(category));
        },
        immediate: true,
        deep: false,
      },
    },
      mounted() {
        this.resizeObserver = new ResizeObserver(() => {
          this.$forceUpdate();
        });
        this.resizeObserver.observe(document.body);

        window.addEventListener("resize", this.updateZoomFactor);
        this.updateZoomFactor();

        const root = document.documentElement;
        const observer = new MutationObserver(() => {
          this.isDarkMode = root.classList.contains('dark-theme');
        });

        observer.observe(root, { attributes: true });
        this.isDarkMode = root.classList.contains('dark-theme');
        // this.selectedCategories = this.numericPreviewData.map(d => d.category);
      },
      updated() {
        if (this.widgetChartType() === "numeric") {
          const type = this.metricData[0]?.type;
          if (type === "priority" && this.priorityOptions.length === 0) {
            this.fetchOptions("priority");
          } else if (type === "status" && this.statusOptions.length === 0) {
            this.fetchOptions("status");
          }
        }
      },
      beforeDestroy() {
        this.resizeObserver.disconnect();
        window.removeEventListener("resize", this.updateZoomFactor);
      },
    methods: {
      removeCard() {
        this.$emit('remove-card');
      },
      handleClose() {
        this.$refs.deleteReportModal.close();
      },
      openCardOptionsDropdown() {
        this.$refs.viewDetail.open();
        this.$refs.insightDetail.getTicketsList();
      },
      openEditModal() {        
        if (this.isEditMode) {
          this.$refs.editWidgetModal.open();

          if (!this.isTemplate) {
            this.customizeModalOpened = true;
          }
          this.$nextTick(() => {
            if (!this.isTemplate) {
              this.$refs.customizeWidget?.initializeData?.();
            } else {
              this.$refs.customizeWidget?.setSelectedMetric?.();
            }
          });
        };
      },
      widgetChartType() {
        if (this.widget.chartType === "horizontalBar") {
          this.isHorizontalBar = true;
          return "bar";
        } 
          this.isHorizontalBar = false;
          return this.widget.chartType;
      },
      fetchOptions(type) {
        const urls = {
          priority: "/priority_options.json",
          status: "/status_options.json",
        };

        const params = type === "priority" ? { company_module: "HelpTicket" } : undefined;

        http
          .get(urls[type], { params })
          .then((res) => {
            const options = res.data.map((o) => {
              const { name, color } = o;
              return {
                id: name,
                name: type === "priority" ? this.titleize(name) : name,
                color,
                label: this.titleize(type),
              };
            }).filter(Boolean);

            if (type === "priority") {
              this.priorityOptions = options;
            } else if (type === "status") {
              this.statusOptions = options;
            }
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error loading ${type} options.`);
          });
      },
      handleFinishEditing({ widgetId, widgetName, widgetDescription, chartType, metrics, index, displayLabels, comparePreviousTimeperiod }) {
        this.displayLabels = displayLabels;
        this.$emit('finalize-widget-edit', { widgetId, widgetName, widgetDescription, chartType, metrics, index, displayLabels, comparePreviousTimeperiod });
        this.customizeModalOpened = false;
        this.$refs.editWidgetModal.close();
      },
      updateZoomFactor() {
        this.zoomFactor = window.devicePixelRatio;
      },
      hexToRgba(hex, alpha = 0.15) {
        const r = parseInt(hex.slice(1, 3), 16);
        const g = parseInt(hex.slice(3, 5), 16);
        const b = parseInt(hex.slice(5, 7), 16);
        return `rgba(${r}, ${g}, ${b}, ${alpha})`;
      },
      handleUnselectedFields(index, unselectedFields) {
        this.$emit('unselected-fields', this.widget.cardAttributes.i, unselectedFields);
      },
      downloadXLSX() {
        this.$refs.insightDetail.downloadXLSX();
      },
    },
  };
</script>

<style lang="scss" scoped>
  ::-webkit-scrollbar {
    height: 0.5rem; /* Scrollbar width */
  }
  ::-webkit-scrollbar-track {
    background: transparent; /* Scrollbar track background */
  }
  ::-webkit-scrollbar-thumb {
    background: var(--themed-fair); /* Scrollbar thumb background */
    border-radius: 4px; /* Rounded corners for thumb */
    transition: background 0.3s;
    opacity: 0.5;
    cursor: default !important;
  }
  ::-webkit-scrollbar-thumb:hover {
    background: var(--themed-very-muted); /* Change thumb color on hover */
  }
  ::-webkit-scrollbar-thumb:active {
    background: var(--themed-very-muted); /* Change thumb color when active */
  }
  .widget-container {
    width: 100%;
    height: 100%;
    border-radius: 0.5rem;
    background-color: white;
    border: 0.5px solid lightgray;
    padding: 1rem;
  }

  .data-container {
    display: flex;
    flex-direction: column;
  }

  .title {
    font-size: 1.2rem;
    z-index: 0;
  }

  .data {
    font-weight: bold;
    font-size: 2rem;
    z-index: 0;
  }
  .remove-card {
    position: absolute;
    top: -0.625rem;
    right: -0.625rem;
    width: 1.5rem;
    height: 1.5rem;
    background-color: lightgray;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 1rem;
    font-weight: bold;
    color: black;
    transition: background-color 0.3s ease;

    &:hover {
      background-color: darkgray;
    }
  }

 .handle-dots {
    font-size: 1.125rem;
    position: absolute;
    top: 36%;
  }

  .drag-overlay {
    background-color: var(--themed-very-fair);
    border-radius: 0.25rem 0 0 0.25rem;
    content: "";
    cursor: move;
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 1.25rem;

    &:hover {
      background-color: rgba(33, 37, 41, 0.3);
    }
  }

  .card-options {
    position: absolute;
    right: 0.625rem;
  }

  .chart-container {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .expand-icon-button {
    cursor: pointer;
    border: none;
    border-radius: 50%;
    background-color: transparent;
    height: 2.125rem;
    padding-left: 6px;
    padding-left: 6px;
    width: 2.125rem;
    position: absolute;
    z-index: 1;
    right: 0.625rem;
    &:focus {
      outline: none;
    }

    &:hover {
      background-color: $light;
    }
  }
  .numeric-data {
    font-weight: bold;
    font-size: 2rem;
  }
  .previous-timeperiod-data {
    font-size: 1rem;
  }
  .higher-data-label {
    color: green;
  }
  .lower-data-label {
    color: red;
  }
  .category-pill {
    border-radius: 2rem;
    margin: 0.3rem;
    padding: 0.1rem 0.6rem 0.1rem 0.6rem;
    font-size: 0.66rem;
    background-color: var(--themed-light);
    color: var(--themed-dark);
  }
  .scroll-wrapper {
    overflow-x: auto;
    white-space: nowrap;
  }
  .numeric-scroll-content {
    display: flex;
    min-width: max-content;
  }
  .export-btn {
    height: fit-content;
  }
</style>
